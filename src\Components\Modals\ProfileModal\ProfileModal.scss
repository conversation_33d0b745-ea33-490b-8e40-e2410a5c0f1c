// Gray scale variables
$gray-100: #f8f9fa;
$gray-200: #e9ecef;
$gray-300: #dee2e6;
$gray-400: #ced4da;
$gray-500: #adb5bd;
$gray-600: #6c757d;
$gray-700: #495057;
$gray-800: #343a40;
$gray-900: #212529;

// Grid breakpoints
$grid-breakpoints: (
  xs: 0,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1200px,
  xxl: 1400px
);

@import '../../../Assets/Styles/colors';
@import '../../../Assets/Styles/typography';
@import '../../../Assets/Styles/mixins';

.profile-modal {
  .modal-content {
    border-radius: 0;
    min-height: 100vh;
    background: $gray-100;
  }

  .modal-header {
    background: $white;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid $gray-300;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .modal-title {
      font-size: 1.5rem;
      font-weight: 600;
      color: $gray-800;
    }

    .edit-profile-btn {
      background: transparent;
      border: 1px solid $success;
      color: $success;
      padding: 0.5rem 1rem;
      border-radius: 20px;
      font-size: 0.9rem;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      transition: all 0.2s ease;

      &:hover {
        background: $success;
        color: $white;
      }

      i {
        font-size: 1rem;
      }
    }
  }

  .modal-body {
    padding: 0;
  }

  .profile-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;

    @media (max-width: map-get($grid-breakpoints, lg)) {
      max-width: 100%;
    }

    @media (max-width: map-get($grid-breakpoints, md)) {
      padding: 1rem;
    }

    .profile-header {
      background: $white;
      padding: 2rem;
      border-radius: 12px;
      display: flex;
      align-items: center;
      gap: 2rem;
      margin-bottom: 2rem;
      box-shadow: 0 2px 8px rgba($gray-900, 0.08);

      @media (max-width: map-get($grid-breakpoints, md)) {
        flex-direction: column;
        text-align: center;
        padding: 1.5rem;
        gap: 1rem;
      }

      .profile-image {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        overflow: hidden;
        background: $gray-200;
        position: relative;
        cursor: pointer;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .profile-initial {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 3rem;
          color: $success;
          background: #e8f5e9;
          position: relative;
        }

        .upload-overlay {
          position: absolute;
          bottom: 0;
          right: 0;
          background: $success;
          color: $white;
          width: 36px;
          height: 36px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.2s ease;
          border: 3px solid $white;

          &:hover {
            background: darken($success, 10%);
            transform: scale(1.1);
          }

          i {
            font-size: 14px;
          }
        }

        &:hover .upload-overlay {
          transform: scale(1.1);
        }

        @media (max-width: map-get($grid-breakpoints, md)) {
          width: 100px;
          height: 100px;

          .upload-overlay {
            width: 30px;
            height: 30px;
            border-width: 2px;

            i {
              font-size: 12px;
            }
          }
        }

        @media (max-width: map-get($grid-breakpoints, sm)) {
          width: 80px;
          height: 80px;

          .upload-overlay {
            width: 24px;
            height: 24px;

            i {
              font-size: 10px;
            }
          }
        }
      }

      .profile-info {
        h2 {
          font-size: 1.8rem;
          margin-bottom: 0.5rem;
          color: $gray-800;

          @media (max-width: map-get($grid-breakpoints, md)) {
            font-size: 1.5rem;
          }

          @media (max-width: map-get($grid-breakpoints, sm)) {
            font-size: 1.3rem;
          }
        }

        .email {
          font-size: 1rem;
          color: $gray-600;
          margin: 0 0 1rem 0;
        }

        .profile-description {
          margin-top: 1rem;

          h4 {
            font-size: 1.1rem;
            color: $gray-800;
            margin-bottom: 0.5rem;
            font-weight: 600;
          }

          .description-edit {
            .description-textarea {
              width: 100%;
              padding: 0.75rem;
              border: 2px solid $gray-300;
              border-radius: 8px;
              font-size: 0.95rem;
              color: $gray-800;
              resize: vertical;
              min-height: 100px;
              font-family: inherit;

              &:focus {
                outline: none;
                border-color: $success;
                box-shadow: 0 0 0 3px rgba($success, 0.1);
              }
            }

            .description-actions {
              display: flex;
              gap: 0.5rem;
              margin-top: 0.75rem;

              button {
                padding: 0.5rem 1rem;
                border-radius: 6px;
                font-size: 0.85rem;
                font-weight: 500;
                display: flex;
                align-items: center;
                gap: 0.3rem;
                transition: all 0.2s ease;

                &.save-btn {
                  background: $success;
                  color: $white;
                  border: none;

                  &:hover {
                    background: darken($success, 10%);
                  }
                }

                &.cancel-btn {
                  background: transparent;
                  color: $gray-600;
                  border: 1px solid $gray-300;

                  &:hover {
                    background: $gray-100;
                  }
                }
              }
            }
          }

          .description-display {
            position: relative;
            padding: 0.75rem;
            background: $gray-100;
            border-radius: 8px;
            border: 1px solid $gray-200;

            .description-text {
              margin: 0;
              color: $gray-700;
              font-size: 0.95rem;
              line-height: 1.5;
            }

            .edit-description-btn {
              position: absolute;
              top: 0.5rem;
              right: 0.5rem;
              background: transparent;
              border: none;
              color: $success;
              padding: 0.25rem;
              border-radius: 4px;
              opacity: 0;
              transition: all 0.2s ease;

              &:hover {
                background: rgba($success, 0.1);
                opacity: 1 !important;
              }
            }

            &:hover .edit-description-btn {
              opacity: 0.7;
            }
          }
        }
      }
    }

    .qa-section {
      background: $white;
      padding: 2rem;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba($gray-900, 0.08);

      @media (max-width: map-get($grid-breakpoints, md)) {
        padding: 1.5rem;
      }

      @media (max-width: map-get($grid-breakpoints, sm)) {
        padding: 1rem;
      }

      .qa-section-header {
        text-align: center;
        margin-bottom: 2rem;

        h3 {
          font-size: 1.8rem;
          margin-bottom: 0.5rem;
          color: $gray-800;
          font-weight: 600;

          @media (max-width: map-get($grid-breakpoints, md)) {
            font-size: 1.5rem;
          }
        }

        .qa-subtitle {
          font-size: 1rem;
          color: $gray-600;
          margin: 0;
          font-style: italic;
        }
      }

      .qa-cards-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 1.5rem;

        @media (max-width: map-get($grid-breakpoints, sm)) {
          grid-template-columns: 1fr;
          gap: 1rem;
        }
      }

      .qa-card {
        background: $white;
        border-radius: 16px;
        overflow: hidden;
        transition: all 0.3s ease;
        border: 1px solid $gray-200;
        box-shadow: 0 2px 8px rgba($gray-900, 0.06);

        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 8px 25px rgba($gray-900, 0.12);
          border-color: rgba($success, 0.3);
        }

        &.editing {
          border-color: $success;
          box-shadow: 0 4px 20px rgba($success, 0.15);
        }

        .qa-card-header {
          background: linear-gradient(135deg, $success 0%, darken($success, 10%) 100%);
          padding: 1rem 1.5rem;
          display: flex;
          align-items: center;
          gap: 1rem;
          position: relative;

          .question-number {
            background: rgba($white, 0.2);
            color: $white;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.9rem;
            flex-shrink: 0;
          }

          .question-title {
            color: $white;
            font-size: 1rem;
            font-weight: 500;
            margin: 0;
            flex: 1;
            line-height: 1.4;

            @media (max-width: map-get($grid-breakpoints, md)) {
              font-size: 0.9rem;
            }
          }

          .edit-question-btn {
            background: rgba($white, 0.2);
            border: none;
            color: $white;
            padding: 0.5rem;
            border-radius: 8px;
            transition: all 0.2s ease;
            opacity: 0;

            &:hover {
              background: rgba($white, 0.3);
              transform: scale(1.1);
            }

            i {
              font-size: 0.85rem;
            }
          }

          &:hover .edit-question-btn {
            opacity: 1;
          }
        }

        .qa-card-content {
          padding: 1.5rem;

          .answer-edit {
            .answer-textarea {
              width: 100%;
              padding: 1rem;
              border: 2px solid $gray-300;
              border-radius: 12px;
              font-size: 0.95rem;
              color: $gray-800;
              resize: vertical;
              min-height: 120px;
              font-family: inherit;
              line-height: 1.5;

              &:focus {
                outline: none;
                border-color: $success;
                box-shadow: 0 0 0 3px rgba($success, 0.1);
              }
            }

            .answer-actions {
              display: flex;
              gap: 0.75rem;
              margin-top: 1rem;
              justify-content: flex-end;

              button {
                padding: 0.6rem 1.2rem;
                border-radius: 8px;
                font-size: 0.85rem;
                font-weight: 500;
                display: flex;
                align-items: center;
                gap: 0.4rem;
                transition: all 0.2s ease;

                &.save-answer-btn {
                  background: $success;
                  color: $white;
                  border: none;

                  &:hover {
                    background: darken($success, 10%);
                    transform: translateY(-1px);
                  }
                }

                &.cancel-answer-btn {
                  background: transparent;
                  color: $gray-600;
                  border: 1px solid $gray-300;

                  &:hover {
                    background: $gray-100;
                  }
                }
              }
            }
          }

          .answer-display {
            .answer-text {
              font-size: 0.95rem;
              color: $gray-700;
              line-height: 1.6;
              margin: 0;
              padding: 0.75rem;
              background: $gray-50;
              border-radius: 8px;
              border-left: 4px solid $success;
              min-height: 80px;
              display: flex;
              align-items: center;
            }
          }
        }
      }
    }

    .error-message {
      background: #f8d7da;
      color: #721c24;
      padding: 0.75rem 1rem;
      border-radius: 4px;
      margin: 1rem 2rem;
      border: 1px solid #f5c6cb;
      font-size: 0.9rem;
    }
  }
}

// Styles for side panel version
.profile-content-wrapper {
  padding: 0;

  .profile-header-bar {
    background: $white;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid $gray-300;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;

    h4 {
      font-size: 1.2rem;
      font-weight: 600;
      color: $gray-800;
      margin: 0;
    }

    .edit-profile-btn {
      background: transparent;
      border: 1px solid $success;
      color: $success;
      padding: 0.4rem 0.8rem;
      border-radius: 20px;
      font-size: 0.8rem;
      display: flex;
      align-items: center;
      gap: 0.4rem;
      transition: all 0.2s ease;

      &:hover {
        background: $success;
        color: $white;
      }

      i {
        font-size: 0.9rem;
      }
    }
  }

  .profile-content {
    padding: 0 1rem 1rem;

    .profile-header {
      background: $white;
      padding: 1.5rem;
      border-radius: 8px;
      margin-bottom: 1rem;
      box-shadow: 0 1px 4px rgba($gray-900, 0.05);
    }

    .qa-section {
      background: $white;
      padding: 1.5rem;
      border-radius: 8px;
      box-shadow: 0 1px 4px rgba($gray-900, 0.05);

      .qa-cards-container {
        grid-template-columns: 1fr;
        gap: 1rem;
      }

      .qa-card {
        .qa-card-header {
          padding: 0.75rem 1rem;

          .question-number {
            width: 24px;
            height: 24px;
            font-size: 0.8rem;
          }

          .question-title {
            font-size: 0.9rem;
          }
        }

        .qa-card-content {
          padding: 1rem;

          .answer-edit .answer-textarea {
            min-height: 80px;
          }

          .answer-display .answer-text {
            min-height: 60px;
            font-size: 0.9rem;
          }
        }
      }
    }
  }

  .error-message {
    background: #f8d7da;
    color: #721c24;
    padding: 0.75rem 1rem;
    border-radius: 4px;
    margin: 0 1rem 1rem;
    border: 1px solid #f5c6cb;
    font-size: 0.9rem;
  }
}